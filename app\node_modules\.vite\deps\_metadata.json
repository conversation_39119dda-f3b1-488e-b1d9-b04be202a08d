{"hash": "f70b0c34", "configHash": "d1289b5a", "lockfileHash": "e3b0c442", "browserHash": "44d1cf57", "optimized": {"@elysiajs/eden": {"src": "../../../../node_modules/@elysiajs/eden/dist/index.mjs", "file": "@elysiajs_eden.js", "fileHash": "a72ed652", "needsInterop": false}, "axios": {"src": "../../../../node_modules/axios/index.js", "file": "axios.js", "fileHash": "7717f3e2", "needsInterop": false}, "pinia": {"src": "../../../../node_modules/pinia/dist/pinia.mjs", "file": "pinia.js", "fileHash": "7b96b125", "needsInterop": false}, "tailwindcss-intersect": {"src": "../../../../node_modules/tailwindcss-intersect/dist/index.esm.js", "file": "tailwindcss-intersect.js", "fileHash": "dff23286", "needsInterop": false}, "vue": {"src": "../../../../node_modules/vue/dist/vue.runtime.esm-bundler.js", "file": "vue.js", "fileHash": "229d8bcc", "needsInterop": false}, "vue-i18n": {"src": "../../../../node_modules/vue-i18n/dist/vue-i18n.mjs", "file": "vue-i18n.js", "fileHash": "9c29ad6b", "needsInterop": false}, "vue-router": {"src": "../../../../node_modules/vue-router/dist/vue-router.mjs", "file": "vue-router.js", "fileHash": "e3bdc1bb", "needsInterop": false}}, "chunks": {"chunk-J256NH3B": {"file": "chunk-J256NH3B.js"}, "chunk-BCMC4SMG": {"file": "chunk-BCMC4SMG.js"}, "chunk-UVKRO5ER": {"file": "chunk-UVKRO5ER.js"}}}