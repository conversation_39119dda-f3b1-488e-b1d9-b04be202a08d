{"version": 3, "sources": ["../../../../node_modules/tailwindcss-intersect/dist/index.esm.js"], "sourcesContent": ["// node_modules/tailwindcss/dist/plugin.mjs\nfunction g(n, i) {\n  return { handler: n, config: i };\n}\ng.withOptions = function(n, i = () => ({})) {\n  function t(o) {\n    return { handler: n(o), config: i(o) };\n  }\n  return t.__isOptionsFunction = true, t;\n};\nvar u = g;\n\n// src/observer/index.js\nvar Observer = {\n  start() {\n    if (document.readyState === \"loading\") {\n      document.addEventListener(\"DOMContentLoaded\", () => this.observe());\n      return;\n    }\n    this.observe();\n  },\n  restart() {\n    this._observers.forEach((observer) => observer.disconnect());\n    this._observers = [];\n    this.observe();\n  },\n  observe() {\n    const selectors = [\n      '[class*=\" intersect:\"]',\n      '[class*=\":intersect:\"]',\n      '[class^=\"intersect:\"]',\n      '[class=\"intersect\"]',\n      '[class*=\" intersect \"]',\n      '[class^=\"intersect \"]',\n      '[class$=\" intersect\"]'\n    ];\n    document.querySelectorAll(selectors.join(\",\")).forEach((element) => {\n      const observer = new IntersectionObserver((entries) => {\n        entries.forEach((entry) => {\n          if (!entry.isIntersecting) {\n            element.setAttribute(\"no-intersect\", \"\");\n            return;\n          }\n          element.removeAttribute(\"no-intersect\");\n          element.classList.contains(\"intersect-once\") && observer.disconnect();\n        });\n      }, {\n        threshold: this._getThreshold(element)\n      });\n      observer.observe(element);\n      this._observers.push(observer);\n    });\n  },\n  _getThreshold(element) {\n    if (element.classList.contains(\"intersect-full\")) {\n      return 0.99;\n    }\n    if (element.classList.contains(\"intersect-half\")) {\n      return 0.5;\n    }\n    return 0;\n  },\n  _observers: []\n};\nvar observer_default = Observer;\n\n// src/index.mjs\nvar index_default = u(\n  ({ addVariant }) => {\n    addVariant(\"intersect\", \"&:not([no-intersect])\");\n  }\n);\nexport {\n  observer_default as Observer,\n  index_default as default\n};\n"], "mappings": ";;;AACA,SAAS,EAAE,GAAG,GAAG;AACf,SAAO,EAAE,SAAS,GAAG,QAAQ,EAAE;AACjC;AACA,EAAE,cAAc,SAAS,GAAG,IAAI,OAAO,CAAC,IAAI;AAC1C,WAAS,EAAE,GAAG;AACZ,WAAO,EAAE,SAAS,EAAE,CAAC,GAAG,QAAQ,EAAE,CAAC,EAAE;AAAA,EACvC;AACA,SAAO,EAAE,sBAAsB,MAAM;AACvC;AACA,IAAI,IAAI;AAGR,IAAI,WAAW;AAAA,EACb,QAAQ;AACN,QAAI,SAAS,eAAe,WAAW;AACrC,eAAS,iBAAiB,oBAAoB,MAAM,KAAK,QAAQ,CAAC;AAClE;AAAA,IACF;AACA,SAAK,QAAQ;AAAA,EACf;AAAA,EACA,UAAU;AACR,SAAK,WAAW,QAAQ,CAAC,aAAa,SAAS,WAAW,CAAC;AAC3D,SAAK,aAAa,CAAC;AACnB,SAAK,QAAQ;AAAA,EACf;AAAA,EACA,UAAU;AACR,UAAM,YAAY;AAAA,MAChB;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACF;AACA,aAAS,iBAAiB,UAAU,KAAK,GAAG,CAAC,EAAE,QAAQ,CAAC,YAAY;AAClE,YAAM,WAAW,IAAI,qBAAqB,CAAC,YAAY;AACrD,gBAAQ,QAAQ,CAAC,UAAU;AACzB,cAAI,CAAC,MAAM,gBAAgB;AACzB,oBAAQ,aAAa,gBAAgB,EAAE;AACvC;AAAA,UACF;AACA,kBAAQ,gBAAgB,cAAc;AACtC,kBAAQ,UAAU,SAAS,gBAAgB,KAAK,SAAS,WAAW;AAAA,QACtE,CAAC;AAAA,MACH,GAAG;AAAA,QACD,WAAW,KAAK,cAAc,OAAO;AAAA,MACvC,CAAC;AACD,eAAS,QAAQ,OAAO;AACxB,WAAK,WAAW,KAAK,QAAQ;AAAA,IAC/B,CAAC;AAAA,EACH;AAAA,EACA,cAAc,SAAS;AACrB,QAAI,QAAQ,UAAU,SAAS,gBAAgB,GAAG;AAChD,aAAO;AAAA,IACT;AACA,QAAI,QAAQ,UAAU,SAAS,gBAAgB,GAAG;AAChD,aAAO;AAAA,IACT;AACA,WAAO;AAAA,EACT;AAAA,EACA,YAAY,CAAC;AACf;AACA,IAAI,mBAAmB;AAGvB,IAAI,gBAAgB;AAAA,EAClB,CAAC,EAAE,WAAW,MAAM;AAClB,eAAW,aAAa,uBAAuB;AAAA,EACjD;AACF;", "names": []}