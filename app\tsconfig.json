{
  "extends": "../tsconfig.json",
  "compilerOptions": {
    "rootDir": "src",
    "outDir": "dist",
    "composite": true,
    "paths": {
      "@/*": ["src/*"],          // <- matches the @/… imports you use in Vue
      "@server/*": ["../server/src/*"]  // <- allows importing from server
    }
  },
  "include": ["src/**/*.ts", "src/**/*.tsx", "src/**/*.vue", "env.d.ts"],
  "exclude": ["../server/**/*"]
}
